// import { BrowserType, webkit, firefox, chromium } from 'playwright'; // Import Playwright's Chromium browser
// import { BrowserContext } from 'playwright-core';
// import { BrowserContextOptions } from 'playwright-core';

import { Page } from "playwright";


// const webkitBrowser: BrowserType = webkit;
// const firefoxBrowser: BrowserType = firefox;
// const chromiumBrowser: BrowserType = chromium;

// export async function setupBrowser(): Promise<void> {

// const browser = await chromium.launch();
// //const browser1 = await webkit.launch();
// const context = await browser.newContext();
// const page = await context.newPage();

// };

// export async function teardown(): Promise<void> {

// // Close the browser
// //const clsoe=  page.context().browser()?.close();

// };

export async function Alllinksvalidation(this: any, { }: { page: Page }) { 
    // This function will return an array of link URLs
const linkElements = await this.page.$$('a');
const links = [];
for (const element of linkElements) {
  const href = await element.getAttribute('href');
  const text = await element.innerText();
  if (href && !href.startsWith('javascript:')) {
    links.push({ text: text.trim(), href: href.trim() });
  }
  return links;
}
}