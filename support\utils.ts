// support/page-utils.ts
import { Page } from 'playwright';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { existsSync as fsExistsSync, mkdirSync } from 'fs';


export class PageUtils {

  // Suppose you have this object from an API response
// static saveReservationDataA(): void {
//   const reservationData = {
//     reservationCode: '7568094',
//     firstName: 'Sadha',
//     appointmentDate: '2025-06-05'
//   };

  // Serialize (convert to JSON string and save to file)
//   writeFileSync('test-data/reservation.json', JSON.stringify(reservationData, null, 2));
// }

// // Deserialize (read JSON file and convert to object)
// static loadReservationData(): any {
//   const dataBuffer = readFileSync('test-data/reservation.json', 'utf-8');
//   const reservationData = JSON.parse(dataBuffer);
//   console.log(reservationData.reservationCode); // Use this in your test
//   return reservationData;
// }

 // Save any test data object to a JSON file
  static saveTestData(data: any, fileName = 'test-data/test-data.json'): void {
    // Ensure the directory exists
    const dir = fileName.substring(0, fileName.lastIndexOf('/'));
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }
    writeFileSync(fileName, JSON.stringify(data, null, 2));
  }

  // Load test data object from a JSON file
  static loadTestData(fileName = 'test-data/test-data.json'): any {
    const dataBuffer = readFileSync(fileName, 'utf-8');
    return JSON.parse(dataBuffer);
  }

  //const closeBtn = page.frameLocator('#kampyleInviteCloseButton,[aria-label="Close"], button[title="Close"]');
  //OR const feedbackIframe = page.frameLocator('iframe[title="Invitation to provide feedback"]');
//const feedbackIframe = page.frameLocator('iframe[id*="kampyleInvite"]');

//or, if you want to match any iframe inside the feedback modal container:
//div[@id="kampyleInviteContainer"]//iframe
static async closeNOThanksModal(page: Page) {
    const closeBtn = await page.frameLocator('//iframe[contains(@id, "kampyleInvite")')
       .locator("button[title='Close']");
    if (await closeBtn.isVisible({ timeout: 4000 })) {
      await closeBtn.click();
    }
  }

  static async closeFeedbackModal(page: Page) {
  try {
    await page.waitForSelector('iframe[id="kampyleInviteContainer"]', { timeout: 5000 });
    // Try to close the feedback modal if present
    // Try iframe close button 
    //iframe[class="ot-text-resize"]
    //const feedbackIframe = page.frameLocator('iframe[title="Invitation to provide feedback"]');
    const feedbackIframe = page.frameLocator('iframe[id="kampyleInviteContainer"]');
    const noThanksButton = feedbackIframe.locator('button:has-text("No, thanks")');
    if (await noThanksButton.isVisible({ timeout: 2000 })) {
      await noThanksButton.click();
      return;
    }
  } catch {
    console.log('Feedback iframe not found or already closed.');
  }

  try {
    // Try direct close button on overlay
    const closeBtn = page.locator('#kampyleInviteCloseButton, [aria-label="Close"], button[title="Close"]');
    if (await closeBtn.isVisible({ timeout: 2000 })) {
      await closeBtn.click();
    }
  } 
  catch {
    console.log('Feedback close button not found or already closed.');
  }
}

  static async closeFeedbackPopup(page: Page) {
    try {
      const feedbackIframe = page.frameLocator('iframe[title="Invitation to provide feedback"]');
      const noThanksButton = feedbackIframe.locator('button:has-text("No, thanks")');
      if (await noThanksButton.isVisible({ timeout: 3000 })) {
        await noThanksButton.click();
      }
    } catch (error) {
      console.warn('Feedback iframe not found or already closed.');
    }
  }

  static async closeCookieBanner(page: Page) {
    try {
      const closeCookiesBtn = page.locator('#onetrust-close-btn-container button');
      if (await closeCookiesBtn.isVisible({ timeout: 3000 })) {
        await closeCookiesBtn.click();
      }
    } catch (error) {
      console.warn('Cookie banner not found or already closed.');
    }
  }

  static async closePopups(page: Page) {
    await this.closeFeedbackPopup(page);
    await this.closeCookieBanner(page);
  }

  static async closeModals(page: Page) {
    // Handle feedback iframe modal
    try {
      const iframeElement = page.frameLocator('iframe[title="Invitation to provide feedback"]');
      const noThanksButton = iframeElement.getByRole('button', { name: 'No, thanks' });
      if (await noThanksButton.count()) {
        await noThanksButton.click();
      }
    } catch (e) {
      // Modal not present, ignore
    }

    // Handle cookie banner
    try {
      const closeBtn = page.locator('//*[@id="onetrust-close-btn-container"]/button');
      if (await closeBtn.count()) {
        await closeBtn.click();
      }
    } catch (e) {
      // Cookie banner not present, ignore
    }
  }

  //another approach
  static async closeModals1(page: Page) {
    try {
      await page.waitForSelector('iframe[title="Invitation to provide feedback"]', { timeout: 5000 });
      const iframeElement = page.frameLocator('iframe[title="Invitation to provide feedback"]');
      const noThanksButton = iframeElement.getByRole('button', { name: 'No, thanks' });
      if (await noThanksButton.count()) {
        await noThanksButton.click();
      }
    } catch (e) {
      // Modal not present, ignore
    }

    try {
      await page.waitForSelector('//*[@id="onetrust-close-btn-container"]/button', { timeout: 5000 });
      const closeBtn = page.locator('//*[@id="onetrust-close-btn-container"]/button');
      if (await closeBtn.count()) {
        await closeBtn.click();
      }
    } catch (e) {
      // Cookie banner not present, ignore
    }
  }

  //second approach
  
}

