import { Before, After, Status, AfterStep } from '@cucumber/cucumber';
import { CustomWorld } from './custom-world';
import { setDefaultTimeout } from '@cucumber/cucumber';

setDefaultTimeout(60 * 1000); // 60 seconds

const browserType = process.env.BROWSER || 'chromium';

Before(async function (this: CustomWorld) {
  await this.launchBrowsers(browserType);
});

// Take screenshot on failure and close browser only once
After(async function (this: CustomWorld, scenario) {
  // Screenshot on failure
  if (scenario.result?.status === Status.FAILED && this.page) {
    const screenshot = await this.page.screenshot();
    await this.attach(screenshot, 'image/png');
  }

  // Soft assertion check
  // if (Array.isArray(this.errors) && this.errors.length > 0) {
  //   const errorMsg = this.errors.map(e => `- ${e}`).join('\n');
  //   throw new Error(`<PERSON>ena<PERSON> failed due to soft assertions:\n${errorMsg}`);
  // }
   if (Array.isArray(this.errors) && this.errors.length > 0) {
    throw new Error('<PERSON><PERSON><PERSON> failed due to soft assertion errors:\n' + this.errors.join('\n'));
  }

  // Close browser
  await this.closeBrowser();
});

// Optional: Screenshot after every step (not required if you only want on failure)
AfterStep(async function (this: CustomWorld) {
  if (this.page && !this.page.isClosed()) {
    const screenshot = await this.page.screenshot();
    await this.attach(screenshot, 'image/png');
  }
});