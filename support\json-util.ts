import { writeFileSync, readFileSync, existsSync } from 'fs';
import path from 'path';

export class JsonUtil {

  private static getFilePath(fileName: string): string {
    //return path.join(__dirname, '../../data', fileName);
    return path.join(process.cwd(), 'data', fileName);
  }

  static saveJson(fileName: string, data: any): void {
    const filePath = this.getFilePath(fileName);
    writeFileSync(filePath, JSON.stringify(data, null, 2));  // Save data with indentation
  }

//   static readJson<T>(fileName: string): T | null {
//     const filePath = this.getFilePath(fileName);
//     if (!existsSync(filePath)) return null;
//     const content = readFileSync(filePath, 'utf-8');
//     return JSON.parse(content) as T;
//   }
// }
static readJson<T>(fileName: string): T | null {
  const filePath = this.getFilePath(fileName);
  console.log("Looking for JSON at:", filePath); //Add this for debugging

  if (!existsSync(filePath)) return null;

  const content = readFileSync(filePath, 'utf-8');
  return JSON.parse(content) as T;
}
}