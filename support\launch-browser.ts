import * as XLSX from 'xlsx';
import path from 'path';

export class ExcelUtil {
  static readTestData(sheetName = 'Sheet1') {
    const excelPath = path.join(__dirname, '../../data/testdata.xlsx');
    const workbook = XLSX.readFile(excelPath);
    const worksheet = workbook.Sheets[sheetName];
    return XLSX.utils.sheet_to_json<{ tc_id: number, username: string, password: string }>(worksheet);
  }
static appendUrl(endpoint : string, path: string) {
  // This function appends a path to a base URL and returns the full URL.
  const baseUrl = 'https://www.cvs.com/';
  const url1 = new URL(path, baseUrl);
  console.log(url1.toString());
  return url1.toString();
}
//Filechooser OR PDF upload function:
static async uploadPdf(page: any, fileWithPath: string = './abc.jpg') {
// let fileWithPath: string = './abc.jpg'
  const [fileChooser] = await Promise.all([
    page.waitForEvent('filechooser'),
    page.getByRole('button', { name: 'Add a Photo' }).click(),
  ]);
  await fileChooser.setFiles([fileWithPath]);
}

// OR

static async uploadPhoto(page: any) {
  // This will resolve the file path relative to the project root
  // for Dynamic const fileWithPath = path.join(process.cwd(), 'abc.jpg'); or
  //const fileWithPath = path.join(__dirname, 'abc.jpg');
  const fileWithPath = './abc.jpg'; // Hardcoded file path
  const [fileChooser]= await Promise.all([
    page.waitForEvent('filechooser'),
    page.getByRole('button', { name: 'Add a Photo' }).click(),
  ]);
  await fileChooser.setFiles([fileWithPath]);
}
}