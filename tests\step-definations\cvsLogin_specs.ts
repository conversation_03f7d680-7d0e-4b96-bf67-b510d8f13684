import { When, Then, Given } from '@cucumber/cucumber';
// import { CustomWorld } from '../../support/world'; // Adjust the path as needed
// Update the path below if 'world' is located elsewhere, or create the file if missing.
import { CustomWorld } from '../../support/custom-world';
import { expect } from 'playwright/test';
import { PageUtils } from '../../support/utils';


Given('User is on the login on cvs home page', async function (this: CustomWorld) {

  await this.page.goto('https://www.cvs.com/');;
  await this.page.waitForTimeout(2000);
  await this.page.screenshot({ path: 'screenshot.png' });

  //to handle ifrme modal
  // await this.page.locator('iframe[title="Invitation to provide feedback"]').contentFrame().getByRole('button', { name: 'No, thanks' }).click();
  // await this.page.locator("//*[@id='onetrust-close-btn-container']/button]").click();

  await this.page.getByRole('button', { name: 'Close' }).click();
  //Dnamic validation
  const pageTitle = await this.page.title(); // this returns the actual <title> text
  expect(pageTitle).toBe('CVS - Online Drugstore, Pharmacy, Prescriptions & Health Information');


  const section = this.page.locator("//*[@id='sec1-link3']/div/span[contains(text(),'Schedule a MinuteClinic appointment')]").click();
  console.log("passed==========");

  // const PageHeader = this.page.locator("//h1[contains(text(), 'CVS - Online Drugstore, Pharmacy, Prescriptions & Health Information')]");
  // if (await PageHeader.count() === 0) {
  //     throw new Error("Page header not found!");
  // }
  // await PageHeader.waitFor({ state: 'visible' });
});


When('user enters valid servicename and clinicname', async function (this: CustomWorld) {

  await PageUtils.closeFeedbackModal(this.page);

  await this.page.waitForTimeout(3000);
  const serviceInput = this.page.locator('.sc-cvs-search-service #service');
  await serviceInput.waitFor({ state: 'visible', timeout: 30000 });
  await serviceInput.click({ force: true });
  await serviceInput.fill('Sore & Strep');

  //await this.page.getByText('Sore & Strep Throat Evaluation', { exact: true });
  //OR
  await this.page.getByText('Sore & Strep Throat Evaluation', { exact: true }).click();
  //await this.page.getByText('Sore & Strep Throat Evaluation').nth(1).click();
  // Select the location
  await this.page.locator('.sc-cvs-location-capture.sub-heading').click();

  // If you need to interact with a shadow DOM, use evaluateHandle directly:
  const inputLocator = this.page
    .locator('cvs-location-capture')
    .locator('input[role="combobox"]');

  await inputLocator.fill('Texarkana, TX');
  await inputLocator.press('Enter');

  // If the input is not in shadow DOM, interact directly as below:
  //await this.page.locator('cvs-location-capture').locator('input[role="combobox"]').fill('New York,75553').then(() => this.page.locator('cvs-location-capture').locator('input[role="combobox"]').press('Enter'));


});

When('user clicks the schedule care button', async function (this: CustomWorld) {
  await this.page.waitForTimeout(3000);
  //kplDeclineButton
  // await this.page.locator('#find-care-btn button').click();
  console.log("Schedule care button clicked");
// Click the "Schedule care" button inside shadow DOM
await this.page.evaluate(() => {
  const shadowHost = document.querySelector('cvs-patient-routing');
  const shadowRoot = shadowHost?.shadowRoot;
  const button = shadowRoot?.querySelector('button.ps-button.ps-button-solid');
  (button as HTMLElement)?.click();
});
});

Then('user should be redirected to the schedule page', async function () {
  const titleText = await this.page.locator('h1').textContent();
  console.log(titleText);

  await expect(this.page.locator('h1')).toHaveText('How do you want to schedule your visit?');
 await this.page.getByRole('link', { name: 'Sign in' }).click(); // Click anywhere to close any open popups
  await this.page.locator("//a[@class='ps-link ps-link-cta ps-link-emphasized']/strong[normalize-space(.)='Sign in']").click();

  //url validation
  const url = await this.page.url();
  console.log('Current URL:', url);
  expect(url).toContain('https://www.cvs.com/signin');

});

Then('user wants to extract all links present on CVS Home page', async function (this: CustomWorld) {
  // Extract all links from the page successfully
  await this.page.waitForLoadState('domcontentloaded');
  await PageUtils.closePopups(this.page);

  // Wait for <a> elements to be present
  await this.page.waitForSelector('a', { timeout: 5000 });
  //print the title of the page
  const title = await this.page.title();
  console.log('Page title:', title);

  // Extract link text and href
  const links = await this.page.$$eval('a', elements =>
    elements
      .filter(a => a.href && !a.href.startsWith('javascript:'))
      .map(a => ({
        text: a.innerText.trim(),
        href: a.href.trim()
      }))
  );

  // Log summary
  console.log(`Found ${links.length} links on CVS Home page`);
  console.log('Links:', links);

  // Attach to test report
  if (this.attach) {
    await this.attach(JSON.stringify(links, null, 2), 'application/json');
  }

  // Capture screenshot
  await this.page.screenshot({ path: 'cvs-home.png', fullPage: true });
});