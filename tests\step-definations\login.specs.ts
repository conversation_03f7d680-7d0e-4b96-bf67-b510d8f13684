import { When, Then, Given} from '@cucumber/cucumber';
//import testData from '../../data/testdata.json';
import * as fs from 'fs'; 
import { CustomWorld } from '../../support/custom-world';// Uncomment and fix the path if excel-util.ts exists
import { ExcelUtil } from '../../support/excel-util'; // Uncomment and fix the path if excel-util.ts exists
// Removed ExcelUtil import and related code as the module does not exist

//firstway with excel readcredentials

//const creds = ExcelUtil.readCredentials('data/testdata.xlsx', 'Sheet1', 1); // uses defaults
// or specify sheet and row
//const creds2 = ExcelUtil.readCredentials('data/testdata.xlsx', 'Sheet1', 1);

// if (creds) {
//   console.log(creds.username, creds.password);
// }

//secondway////////

// const excelTestData = ExcelUtil.readTestData(); // Default is 'Sheet1'
// console.log(excelTestData);



// // Get the username for tc_id 1
// const username = ExcelUtil.getCellValue(1, 'username');
// console.log(username);

// // Get the password for tc_id 2
// const password = ExcelUtil.getCellValue(2, 'password');
// console.log(password);

// Read and parse the JSON file synchronously


//Read from JSON file
const raw = fs.readFileSync('data/testdata.json', 'utf-8');
const testData = JSON.parse(raw);

/////////////Steps implementations/////
Given('User is on the login page', async function (this: CustomWorld) {
  await this.page?.goto('https://saucedemo.com');
});

When('user enters valid username and password', async function (this: CustomWorld) {
  // await this.page.fill('#user-name', 'standard_user');
  // await this.page.fill('#password', 'secret_sauce');
  //reading data from Json file
  await this.page.screenshot({ path: 'screenshot.png' });
  await this.page.waitForSelector('#user-name',{timeout: 5000});
  await this.page.fill('#user-name',testData.username);
  await this.page.waitForTimeout(2000);
   await this.page.fill('#password',testData.password);
});

////excel read
// When('user enters valid username and password', async function (this: CustomWorld) {
 

//   await this.page.waitForSelector('#user-name',{timeout: 5000});
//   await this.page.fill('#user-name',ExcelUtil.readTestData()[0].username);
//   //or
//   const Username = ExcelUtil.getCellValue(1, 'username');
//   await this.page.fill('#user-name', Username ?? '');
  
//    this.page.waitForTimeout(2000);
//    await this.page.fill('#password',ExcelUtil.readTestData()[0].password);
// });


When('user clicks the login button', async function (this: CustomWorld) {
    await this.page.waitForSelector('#login-button', { state: 'visible' });
  await this.page.screenshot({ path: 'screenshot.png' });
    await this.page.click('#login-button');
});

Then('user should be redirected to the dashboard', async function (this: CustomWorld) {
  const title = await this.page.title();
  if(title == "Swag Labs"){
    console.log(title, "valid title");
  }else{
    console.log(title, "invalid title");
  }
  await this.page.waitForURL('**https://www.saucedemo.com/inventory.html'); // Adjust the pattern as needed for your app
await this.browser.close();
});

Given('user enters invalid username and password', async function (this: CustomWorld) {
  await this.page.waitForSelector('#user-name',{timeout: 5000});
  await this.page.fill('#user-name', "invalid_user");
  await this.page.waitForTimeout(2000);
   await this.page.fill('#password',"invalid_password");
});

Then('user should see an error message', async function (this: CustomWorld) {
  const title = await this.page.title();
  if(title == "Swag Labs"){
    console.log(title, "valid title");
  }else{
    console.log(title, "invalid title");
  }
  await this.page.waitForURL('**https://www.saucedemo.com/inventory.html'); // Adjust the pattern as needed for your app
await this.browser.close();
});