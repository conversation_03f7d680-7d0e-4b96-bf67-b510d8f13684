const fs = require('fs');

const input = './json-report/cucumber-report.json';
const output = './reports/arc-style-like.json';

const raw = fs.readFileSync(input);
const report = JSON.parse(raw);

// Transform into a custom "arc-style-like" structure
const simplified = report.flatMap(feature =>
  feature.elements.map(scenario => ({
    feature: feature.name,
    scenario: scenario.name,
    status: scenario.steps.every(s => s.result.status === 'passed') ? 'passed' : 'failed',
    steps: scenario.steps.map(s => ({
      step: s.name,
      keyword: s.keyword,
      result: s.result.status,
      duration: s.result.duration
    }))
  }))
);

fs.writeFileSync(output, JSON.stringify(simplified, null, 2));
console.log(`Simplified JSON report written to: ${output}`);