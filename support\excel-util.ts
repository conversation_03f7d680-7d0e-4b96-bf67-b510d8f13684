import * as XLSX from 'xlsx';
import path from 'path';
import ExcelJS from 'exceljs';

export class ExcelUtil {

  static readCredentials(filePath = 'TestData/TestData.xlsx', p0: string, p1: number) {
    const workbook1 = XLSX.readFile(filePath);//// Load workbook
    const sheetName1 = workbook1.SheetNames[0];
    const worksheet1 = workbook1.Sheets[sheetName1]; //// Select sheet
    const row = XLSX.utils.sheet_to_json(worksheet1);//// Convert to JSON

    // Assuming the first row has the credentials
    const { username, password } = row[0] as { username: string; password: string };
    console.log(`username: ${username}, password: ${password}`);

    return { username, password };
  }
            ////////OR/////

static async readTestData(filePath: string, sheetName: string): Promise<any[]> {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.readFile(filePath);
  const worksheet = workbook.getWorksheet(sheetName);

  const rows: any[] = [];
  if (worksheet) {
    worksheet.eachRow((row, rowNumber) => {
      // Assuming the first row is header
      if (rowNumber === 1) return;

      const rowData: Record<string, any> = {};
      row.eachCell((cell, colNumber) => {
        const header = worksheet.getRow(1).getCell(colNumber).text;
        rowData[header] = cell.text;
      });

      rows.push(rowData);
    });
  }

  return rows;
}
}