// import { test, expect } from '@playwright/test';

// test('the user enters valid username and password', async ({ page }) => {
//         // Navigate to a URL
//         await page.goto('https://saucedemo.com');

//         const title = await page.title();
//         expect(title).toBe("Swag Labs");
//         // Perform actions to locators
//         await page.locator('#user-name').fill('Jyothi'); // Replace with the actual username
//         await page.locator('#password').fill('@jpp');
//         //await page.fill('#username', 'your-username'); 
//         await page.click('#login-button');

//         // Wait for navigation or a specific element
//         //await page.waitForSelector('#dashboard'); // Replace with the actual selector

//         await page.waitForURL('**https://www.saucedemo.com/'); // Adjust the pattern as needed for your app
//     });
// test('has title', async ({ page }) => {
//   await page.goto('https://playwright.dev/');

//   // Expect a title "to contain" a substring.
//   await expect(page).toHaveTitle(/Playwright/);
// });

// test('get started link', async ({ page }) => {
//   await page.goto('https://playwright.dev/');

//   // Click the get started link.
//   await page.getByRole('link', { name: 'Get started' }).click();

//   // Expects page to have a heading with the name of Installation.
//   await expect(page.getByRole('heading', { name: 'Installation' })).toBeVisible();
// });
