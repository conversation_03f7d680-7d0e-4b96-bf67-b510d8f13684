// import { test, expect, chromium, <PERSON><PERSON><PERSON>, <PERSON> } from '@playwright/test';

// test.describe('Login Tests', () => {

//  test.skip('basic test', async ({ page }) => {
//         await page.goto('https://example.com');
//         const title = await page.title();
//         expect(title).toBe('Example Domain');
//     });

//  test('user is on the login page', async function () {
//   const browser: Browser = await chromium.launch({ headless: true });
//   const context = await browser.newContext();
//   const page: Page = await context.newPage();
//   await page.goto('https://playwrightexample.com');
//   //await browser.close();
// });

//  test('the user enters valid username and password', async ({ page }) => {
//         // Navigate to a URL
//         await page.goto('https://playwright.com');

//         const title = await page.title();
//         expect(title).toBe("Swag Labs");
//         // Perform actions to locators
//         await page.locator('#user-name').fill('Jyothi'); // Replace with the actual username
//         await page.locator('#password').fill('@jpp');
//         //await page.fill('#username', 'your-username'); 
//         await page.click('#login-button');

//         // Wait for navigation or a specific element
//         //await page.waitForSelector('#dashboard'); // Replace with the actual selector

//         await page.waitForURL('**https://www.playwright.com/'); // Adjust the pattern as needed for your app
//     });
// });