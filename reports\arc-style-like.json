[{"feature": "Login Functionality", "scenario": "Validate user is able to login Successfully on cvs home page", "status": "failed", "steps": [{"keyword": "Before", "result": "passed", "duration": 760597699}, {"step": "User is on the login on cvs home page", "keyword": "Given ", "result": "passed", "duration": 9254850299}, {"step": "user enters valid servicename and clinicname", "keyword": "And ", "result": "failed", "duration": 33278092800}, {"step": "user clicks the schedule care button", "keyword": "When ", "result": "skipped", "duration": 0}, {"step": "user should be redirected to the schedule page", "keyword": "Then ", "result": "skipped", "duration": 0}, {"keyword": "After", "result": "passed", "duration": 453182200}]}, {"feature": "Login Functionality", "scenario": "Validate user is able to login Successfully on cvs home page", "status": "passed", "steps": [{"keyword": "Before", "result": "passed", "duration": 679194300}, {"step": "User is on the login on cvs home page", "keyword": "Given ", "result": "passed", "duration": 9232248498}, {"step": "user wants to extract all links present on CVS Home page", "keyword": "Then ", "result": "passed", "duration": 4792936200}, {"keyword": "After", "result": "passed", "duration": 273810700}]}]