Feature: Login Functionality
  As a user, I want to log into the cvs application

  @smokeCVS
  Scenario: Validate user is able to login Successfully on cvs home page
    Given User is on the login on cvs home page
    And user enters valid servicename and clinicname
    When user clicks the schedule care button
    Then user should be redirected to the schedule page

 @smokeCVS1
  Scenario: Validate user is able to login Successfully on cvs home page
    Given User is on the login on cvs home page
    Then user wants to extract all links present on CVS Home page