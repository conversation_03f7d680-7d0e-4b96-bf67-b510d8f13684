{"name": "typescript_demo", "version": "1.0.0", "main": "index.js", "scripts": {"test": "npx cucumber-js --format json:./json-report/cucumber-report.json", "report": "node generate-report.js", "json-summary": "node generate-json-summary.js", "test:report": "npm run test && npm run report", "preinstall": "npx npm-force-resolutions", "test:parallel": "npx cucumber-js --parallel 2 --format json:./json-report/cucumber-report.json", "test:parallel:report": "npm run test:parallel && npm run report", "test:parallel:smoke": "npx cucumber-js --tags @smokes --parallel 2 --format json:./json-report/cucumber-report.json", "test:serialize": "npx cucumber-js --tags @dataTest --require-module ts-node/register --require tests/step-def/LoginSerialize.ts --format json:./json-report/cucumber-report.json"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@cucumber/cucumber": "^11.3.0", "@cucumber/pretty-formatter": "^1.0.1", "@playwright/test": "^1.52.0", "@types/node": "^22.15.17", "@types/xlsx": "^0.0.35", "@wdio/allure-reporter": "^9.14.0", "cucumber-html-reporter": "^6.0.0", "exceljs": "^4.4.0", "multiple-cucumber-html-reporter": "^3.9.2", "npm-force-resolutions": "^0.0.10", "playwright": "^1.52.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "uuid": "^9.0.1"}, "resolutions": {"har-validator": "latest"}, "dependencies": {"xlsx": "^0.18.5"}}