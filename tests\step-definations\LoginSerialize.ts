import { Given, When, Then } from '@cucumber/cucumber';
import { JsonUtil } from '../../support/json-util'; // Adjust path as needed
import { CustomWorld } from 'support/custom-world'; 
// Try capital 'W' if the file is named 'World.ts'

interface ReservationData {
  username: string;
  password: string;
  reservationCode: string;
}

let userData: ReservationData;

Given('User is on the home page', async function (this: CustomWorld) {
  await this.page?.goto('https://www.saucedemo.com');
});

When('user enter valid username and password', async function (this: CustomWorld) {
  userData = JsonUtil.readJson<ReservationData>('reservation.json')!;
  if (!userData) {
    throw new Error('No reservation data found!');
  }

  await this.page.waitForSelector('#user-name', { timeout: 5000 });
  await this.page.fill('#user-name', userData.username);
  await this.page.fill('#password', userData.password);
});

When('user click the login button', async function (this: CustomWorld) {
  await this.page.waitForSelector('#login-button', { timeout: 5000 });
  await this.page.click('#login-button');
});

Then('user should be on redirected to the dashboard', async function (this: CustomWorld) {
  await this.page.waitForURL('**/inventory.html', { timeout: 5000 });

  const title = await this.page.title();
  if (title === 'Swag Labs') {
    console.log(' Successfully landed on dashboard');
  } else {
    console.log('Unexpected title:', title);
  }

  await this.browser?.close();
});