// Import both reporters
const multipleCucumberHtmlReporter = require('multiple-cucumber-html-reporter');
const cucumberHtmlReporter = require('cucumber-html-reporter');

//to run command npm run test & npm run report

// === 1. Multiple Cucumber HTML Reporter ===
multipleCucumberHtmlReporter.generate({
  jsonDir: './json-report',
  reportPath: './reports/multiple-html',
  displayDuration: true,
  pageTitle: 'BDD Test Report',
  reportName: 'Cucumber BDD Test Dashboard',
  displayDuration: true,
  openReportInBrowser: true, //This auto-opens the report
  metadata: {
    browser: {
      name: 'chrome',
      version: '114'
    },
    device: 'Local Test Machine',
    platform: {
      name: 'Windows',
      version: '10'
    }
  },
  customData: {
    title: 'Execution Info',
    data: [
      { label: 'Project', value: 'BDD Demo' },
      { label: 'Release', value: '1.0.0' },
      { label: 'Cycle', value: 'Regression' },
      { label: 'Execution Start', value: new Date().toLocaleString() },
      { label: 'Execution End', value: new Date().toLocaleString() }
    ]
  }
});

// === 2. Classic Cucumber HTML Reporter (Arc Style) ===
cucumberHtmlReporter.generate({
  theme: 'bootstrap', // Also available: 'hierarchy', 'foundation', 'simple'
  jsonFile: './json-report/cucumber-report.json',
  output: './reports/arc-style-report.html',
  reportSuiteAsScenarios: true,
  launchReport: true, //  This line will auto-open the HTML report
  metadata: {
    "App Version": "1.0.0",
    "Test Environment": "STAGING",
    "Browser": "Chrome 114",
    "Platform": "Windows 10",
    "Executed": "Local"
  }
});
