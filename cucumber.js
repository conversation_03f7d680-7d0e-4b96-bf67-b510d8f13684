module.exports = {
  default: {

    require: [
      'support/hook.ts',
      'support/custom-world.ts',
      'tests/step-definations/**/*.ts'
    ],
    requireModule: ['ts-node/register'],
    //worldParameters: {},
    format: ['@cucumber/pretty-formatter',
      'html:reports/cucumber-report.html',
      'json:reports/cucumber-report.json'],
    
    paths: ['src/features/**/*.feature'],
    tags: process.env.TAGS || "",
    //publishQuiet: true,
    //'--format progress': true,
    //'publish-quiet': true,
    parallel: 2,
    world: './support/custom-world.ts', // Register your world
    //"setTimeout": 60000,
  }
};