Feature: Login Functionality
  As a user, I want to log into the application

  @dataTest
  Scenario: Validate user is able to login Successfully
    Given User is on the home page
    And user enter valid username and password
    When user click the login button

  @dat
  Scenario: Validate user is able to login Successfully
    Given User is on the login page
    And user enters valid username and password
    When user clicks the login button
    Then user should be on redirected to the dashboard

  #@smo
  # Scenario: Validate user is able to login with valid credentials
  #   Given User is on the login page
  #   And user enters invalid username and password
  #   When user clicks the login button
  #   Then user should be on redirected to the dashboard
  #take one excel sheet , sheet no.1 made two column of user name amd password and tc id
    # run one tc and pass or Read the value 
    #CREate a object of excel sheet and create an on=bject of sheet one as well 
    #then pass to the steps and try to get in loop from excel sheet
