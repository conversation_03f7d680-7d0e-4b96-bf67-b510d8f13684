<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <!-- Meta, title, CSS, favicons, etc. -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Cache-control" content="public">
    <title>Multiple Cucumber HTML Reporter</title>

    
            <!-- Bootstrap -->
            <link rel="stylesheet" href="../assets/css/bootstrap.min.css" >
            <link rel="stylesheet" href="../assets/css/dataTables.bootstrap.min.css" >
            <link rel="stylesheet" href="../assets/css/responsive.bootstrap5.min.css" >

            <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
            <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->

            <!--[if lt IE 9]>
            <script src="../assets/js/html5shiv.min.js"></script>
            <script src="../assets/js/respond.min.js"></script>
            <![endif]-->

            <link href="../assets/css/font-awesome.min.css" rel="stylesheet">
    
    <!-- Darkmode -->
    <script>
        var darkMode = 'darkmode'

function applyDarkMode() {
    document.getElementById('features-table').classList.toggle('table-striped');
    applyFontStyle();
}

function saveState() {
    if(isDarkModeOn()) {
        window.localStorage['darkmode'] = 'on';
    } else {
        window.localStorage['darkmode'] = 'off';
    }
}

function applyFontStyle() {
    document.body.classList.toggle(darkMode);
}


function isDarkModeOn() {
    var toggle = document.getElementById('darkCheck');
    return toggle.checked;
}

window.onload = function() {
    if(window.localStorage['darkmode'] === 'on') {
        applyDarkMode();
        document.getElementById('darkCheck').checked = true;
    }
}

    </script>

    <!-- Custom Theme Style -->
    <style type="text/css">
        body {
  color: #73879c;
  background: #f7f7f7;
  font-family: "Helvetica Neue", Roboto, Arial, "Droid Sans", sans-serif;
  font-size: 13px;
  font-weight: 400;
  line-height: 1.471;
}

.main_container {
  padding: 10px 20px 0;
}

i span {
  display: none;
}

/* Navigation */
nav.navbar {
  background: #ededed;
  border-bottom: 1px solid #d9dee4;
  margin-bottom: 0;
}

nav .navbar-brand {
  border-right: 1px solid #d9dee4;
  color: #5a738e;
}

nav .navbar-text {
  font-size: 18px;
  height: 50px;
  margin-bottom: 0;
  margin-top: 0;
  padding: 15px 0;
  float: right;
}

/* Table */
table {
  width: 100%;
}

table.chart tr th:first-of-type {
  width: 33.333%;
}

.table > thead > tr > th {
  background: #f5f7fa;
}

table.tile h3,
table.tile h4,
table.tile span {
  font-weight: bold;
  vertical-align: middle !important;
}

table.tile th,
table.tile td {
  text-align: center;
}

table.tile th {
  border-bottom: 1px solid #e6ecee;
}

table.tile td {
  padding: 5px 0;
}

table.tile td ul {
  text-align: left;
  padding-left: 0;
}

table.tile td ul li {
  list-style: none;
  width: 100%;
}

table.tile td ul li a {
  width: 100%;
}

table.tile td ul li a big {
  right: 0;
  float: right;
  margin-right: 13px;
}

table.tile_info {
  width: 100%;
}

table.tile_info td {
  text-align: left;
  padding: 1px;
  font-size: 15px;
}

table.tile_info td p {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
  line-height: 28px;
}

table.tile_info td i {
  display: inline-block;
  margin-right: 8px;
  font-size: 17px;
  float: left;
  width: 18px;
  line-height: 28px;
  text-align: center;
}

table.tile_info td:first-child {
  width: 65%;
}

td span {
  line-height: 28px;
}

table.tile_info td.percentage {
  text-align: right;
}

/* chart in table */
table td.chart {
  display: inline-block;
  position: relative;
}

table td.chart #feature-chart,
table td.chart #scenario-chart,
table td.chart .total {
  height: 140px;
  margin: 15px 10px 10px 0;
  width: 140px;
}

table td.chart .total {
  display: inline-block;
  position: absolute;
  font-size: 2em;
  height: 50px;
  line-height: 50px;
  top: 45px;
  left: 45px;
  text-align: center;
  vertical-align: middle;
  width: 50px;
}

/* colors */
.ambiguous-color {
  color: #e74c3c !important;
}

.failed-color {
  color: #e74c3c !important;
}

.not-defined-color {
  color: #f39c12 !important;
}

.passed-color {
  color: #1abb9c !important;
}

.pending-color {
  color: #ffd119 !important;
}

.skipped-color {
  color: #3498db !important;
}

/* backgrounds */
.ambiguous-background {
  background: #b73122 !important;
}

.failed-background {
  background: #e74c3c !important;
}

.not-defined-background {
  background: #f39c12 !important;
}

.passed-background {
  background: #1abb9c !important;
}

.pending-background {
  background: #ffd119 !important;
}

.skipped-background {
  background: #3498db !important;
}

/* general */
.x_panel {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px 17px;
  display: inline-block;
  background: #fff;
  border: 1px solid #e6e9ed;
  -webkit-column-break-inside: avoid;
  opacity: 1;
}

.x_title {
  border-bottom: 2px solid #e6e9ed;
  padding: 1px 5px 6px;
  margin-bottom: 10px;
}

.x_title .filter {
  width: 40%;
  float: right;
}

.x_title h2 {
  margin: 5px 0 6px;
  float: left;
  font-size: 24px;
  font-weight: 400;
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.fixed_height_320 {
  height: 320px;
}

.x_title span {
  color: #bdbdbd;
}

.x_content {
  padding: 0 5px 6px;
  position: relative;
  width: 100%;
  float: left;
  clear: both;
  margin-top: 5px;
}

.x_content h4 {
  font-size: 16px;
  font-weight: 500;
}

.panel_toolbox {
  float: right;
  margin: 5px 0 0;
  min-width: 70px;
}

.panel_toolbox > li {
  float: right;
}

.panel_toolbox > li > a {
  cursor: pointer;
}

.panel_toolbox > li > a {
  padding: 5px;
  color: #c5c7cb;
  font-size: 14px;
}

.panel_toolbox > li > a:hover {
  background: #f5f7fa;
}

.page-title {
  width: 100%;
  padding: 10px 0 30px 0;
}

.page-title {
  display: block;
}

.page-title h1 {
  margin: 9px 0 9px 13px;
  font-size: 30px;
}

.page-title .title_right {
  width: 55%;
  float: left;
  display: block;
}

.page-title .title_right .pull-right {
  margin: 10px 0;
}

.page-title p {
  margin-left: 15px;
}

.dashboard-widget-content {
  padding-top: 9px;
}

.dashboard-widget-content .sidebar-widget {
  width: 50%;
  display: inline-block;
  vertical-align: top;
  background: #fff;
  border: 1px solid #abd9ea;
  border-radius: 5px;
  text-align: center;
  float: right;
  padding: 2px;
  margin-top: 10px;
}

ul.quick-list {
  padding-left: 0;
  display: inline-block;
}

ul.quick-list li,
table.quick-list tr {
  padding-left: 10px;
  list-style: none;
  margin: 0;
  padding-bottom: 6px;
  padding-top: 4px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

ul.quick-list li .meta-data-title,
table.quick-list td.meta-data-title {
  display: inline-block;
  min-width: 75px;
  font-weight: bold;
}

ul.quick-list li span,
table.quick-list td {
  line-height: 28px;
}

ul.quick-list li,
table.quick-list tr {
  border-bottom: 1px solid #efefef;
  padding: 0.5em 0;
}

ul.quick-list li:last-child,
table.quick-list tr:last-child {
  border-bottom: none;
}

ul.quick-list li i {
  padding-right: 10px;
  color: #757679;
}

.screenshot {
  max-height: 100%;
  max-width: 100%;
}

.videoCapture {
  width: 50%;
  height: 50%;
  max-height: 100%;
  max-width: 100%;
}

/* Features / Scenarios */
ul.panel_toolbox li .step {
  border-radius: 50%;
  color: #ffffff;
  display: block;
  font-size: 14px;
  height: 30px;
  margin-right: 5px;
  padding: 5px;
  text-align: center;
  width: 30px;
}

.scenario-step-container {
  margin-bottom: 10px;
}

.scenario-step-container .label {
  display: inline-block;
  text-align: center;
  width: 30px;
}

.scenario-step-container .text {
  display: inline;
}

.scenario-step-container .duration {
  position: relative;
  float: right;
}

.scenario-step-container .text .keyword.highlight {
  font-size: 1.2em;
  font-weight: 700;
}

.scenario-scroll-bar {
  overflow-x: scroll;
}

.scenario-step-collapse,
.scenario-scroll-bar .arguments {
  margin-left: 30px;
  width: auto;
}

/* media */
@media (max-width: 1200px) {
  .x_title h2 {
    width: 70%;
  }
}

@media (max-width: 640px) {
  .x_title h2 {
    width: 100%;
  }
}

/* override */
table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child:before {
  background: #1abb9c;
}

table.dataTable.dtr-inline.collapsed
  > tbody
  > tr.parent
  > td:first-child:before,
table.dataTable.dtr-inline.collapsed
  > tbody
  > tr.parent
  > th:first-child:before {
  background: #e74c3c;
}

.created-by {
  padding: 50px 0;
  text-align: center;
}

body.darkmode,
body.darkmode div.created-by,
body.darkmode .dropdown-menu {
  background: #212121 !important;
  color: #b4bfca;
  border-color: #2e2e2e;
}

body.darkmode .x_panel {
  background: #1c1c21 !important;
  color: #b4bfca;
  border: 1px solid #2e2e2e;
}

body.darkmode div.container-fluid {
  background: #2b2b2b !important;
  color: #b4bfca;
  border: 0 solid #2e2e2e;
}

body.darkmode nav.navbar {
  background: #2b2b2b;
  border-bottom: 1px solid #313a45;
  margin-bottom: 0;
}

body.darkmode .x_title {
  background: #1c1c21 !important;
  border-bottom: 2px solid #2e2e2e;
  padding: 1px 5px 6px;
  margin-bottom: 10px;
}

body.darkmode table.quick-list tr {
  border-bottom: 1px solid #292929;
  padding: 0.5em 0;
}

body.darkmode ul.quick-list li,
body.darkmode table.quick-list tr {
  border-bottom: 1px solid #2e2e2e;
  padding: 0.5em 0;
}

.darkModeIcon {
  font-size: x-large;
  float: left;
  padding-top: 12px;
  padding-left: 4px;
  cursor: pointer;
  color: #b4bfca;
  border-color: #2e2e2e;
}

.darkModeIcon:before {
  content: "";
}

body.darkmode a.navbar-default,
body.darkmode a.navbar-brand {
  color: #b1bfcd;
  background: transparent !important;
  border-right: 1px solid #313a45;
}

body.darkmode a {
  background: transparent !important;
  color: #9cc2e3;
  border-color: #2e2e2e;
}

body.darkmode a.collapse-link {
  color: #c7c7c7;
  border-color: #2e2e2e;
}

body.darkmode li:not([class]) > a[id] {
  color: white;
}
body.darkmode li:not([class]) > a[id]:hover {
  background: darkslateblue;
}

body.darkmode #features-table {
  color: #b4bfca;
  border-color: #2e2e2e;
}

body.darkmode #features-table th {
  background: #212121 !important;
  color: #a3c2db;
  border-color: #2e2e2e;
}

body.darkmode #features-table td {
  border-color: #2e2e2e;
}

body.darkmode #features-table tr:nth-of-type(odd) {
  background: #212121 !important;
  border-color: #2e2e2e;
}

body.darkmode table.dataTable > tbody > tr.child ul.dtr-details li {
  border-bottom: 1px solid #2e2e2e;
  padding: 0.5em 0;
}

body.darkmode .pagination,
body.darkmode .pagination > .active > a,
body.darkmode .pagination > .active > a:focus,
body.darkmode .pagination > .active > a:hover,
body.darkmode .pagination > .active > span,
body.darkmode .pagination > .active > span:focus,
body.darkmode .pagination > .active > span:hover {
  background: #3a7ab7;
  border-color: #3a7ab7;
}

body.darkmode .pagination > .disabled > a,
body.darkmode .pagination > .disabled > a:focus,
body.darkmode .pagination > .disabled > a:hover,
body.darkmode .pagination > .disabled > span,
body.darkmode .pagination > .disabled > span:focus,
body.darkmode .pagination > .disabled > span:hover {
  background: #3a7ab7;
  border-color: #3b3b3b;
  color: #bfbfbf;
}

body.darkmode
  table.dataTable.dtr-inline.collapsed
  > tbody
  > tr
  > td:first-child::before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child::before {
  border: 1px solid #2e2e2e;
}

body.darkmode #features-table > thead > tr {
  color: #b4bfca;
  border-color: #2e2e2e;
}

body.darkmode .form-control {
  background: #212121 !important;
  color: #c3c3c3;
  border-color: #2e2e2e;
}
body.darkmode li[id*="features"] > a {
  background: #212121 !important;
}

body.darkmode .btn-info {
  color: #b4bfca;
  background: #337ab7;
  border-color: #337ab7;
}

body.darkmode .panel_toolbox > li > a:hover {
  background: transparent;
}

body.darkmode .table-striped > tbody > tr:nth-of-type(even) {
  background: #1c1c21;
}

body.darkmode div pre {
  background: #212121 !important;
  color: #b4bfca;
}

body.darkmode span.tag {
  color: #b4bfca;
}

body.darkmode .keyword {
  color: #b4bfca;
}

body.darkmode div.tags ~ h1 {
  color: #b4bfca;
}

body.darkmode div.tags ~ h2 {
  color: #b4bfca;
}

body.darkmode div.tags ~ h1 small {
  color: #bfbfbf;
}

body.darkmode div.tags ~ h2 small {
  color: #bfbfbf;
}

svg#moon,
svg#sun {
  width: 40px;
  height: 30px;
  fill: #ec6d04;
}

input#darkCheck:checked ~ label > svg#sun {
  display: none;
  border-color: #212121;
}

label > svg#moon {
  display: none;
}

input#darkCheck:checked ~ label > svg#moon {
  display: inline-block;
  fill: #b4bfca;
  border-color: #212121;
}

        
    </style>
</head>
<body>
    <script type="text/javascript">
        window.onload = function() {
            if(window.localStorage['darkmode'] === 'on') {
                applyFontStyle();
            }
        }
    </script>
    <nav class="navbar">
        <div class="container-fluid">

            <div class="navbar-header">
                <a class="navbar-brand" href="#" onclick="history.back(-1)">
                    <i class="fa fa-arrow-left"><span>Back</span></i>
                </a>
            </div>
            <p class="navbar-text" style="float: left">Features Overview
            </p>
            <p class="navbar-text">Multiple Cucumber HTML Reporter</p>
        </div>

    </nav>

    <div class="main_container">

        <div class="page-title">
            <div class="tags">
                
                    
                    
                
            </div>
            <h1>Feature:
                <small>Login Functionality</small>
            </h1>
            
                <p>
                    <strong>Description: </strong>   As a user, I want to log into the application </p>
                
               <p><strong>File name:</strong>
                    login.feature
                </p>
                <p><strong>Relative path:</strong>
                    features/login.feature</p>
            
        </div>

        <div class="clearfix"></div>

        <div class="row">
            <div class="col-md-8 col-xs-12">
                <div class="x_panel fixed_height_320">
                    <div class="x_title">
    <h2>Scenarios</h2>
    <ul class="nav navbar-right panel_toolbox">
        <li>
            <a class="collapse-link">
                <i class="fa fa-chevron-up"></i>
            </a>
        </li>
    </ul>
    <div class="clearfix"></div>
</div>
<div class="x_content">
    <table class="chart">
        <tr>
            <th>
                <p>Chart</p>
            </th>
            <th>
                <div class="col-lg-7 col-md-7 col-sm-7 col-xs-7">
                    <p class="">Status</p>
                </div>
                <div class="col-lg-5 col-md-5 col-sm-5 col-xs-5">
                    <p class="" style="float:right;">Progress</p>
                </div>
            </th>
        </tr>
        <tr>
            <td class="chart">
                <canvas id="scenario-chart"></canvas>
                <div class="total">1</div>
            </td>
            <td>
                <table class="tile_info">
                    <tr>
                        <td>
                            <p data-bs-toggle="tooltip" data-placement="left" title="Scenario passed">
                                <i class="fa fa-check-circle passed-color"></i>
                                Passed
                            </p>
                        </td>
                        <td class="percentage">100.00 %</td>
                    </tr>
                    <tr>
                        <td>
                            <p data-bs-toggle="tooltip" data-placement="left" title="Scenario failed">
                                <i class="fa fa-exclamation-circle failed-color"></i>
                                Failed
                            </p>
                        </td>
                        <td class="percentage">0.00 %</td>
                    
                    
                    </tr>
                    
                    
                </table>
            </td>
        </tr>
        
    </table>
</div>

                </div>
            </div>
            
            <div class="col-md-4 col-xs-12">
                <div class="x_panel fixed_height_320">
                    <div class="x_title">
    <h2>Metadata</h2>
    <ul class="nav navbar-right panel_toolbox">
        <li>
            <a class="collapse-link">
                <i class="fa fa-chevron-up"></i>
            </a>
        </li>
    </ul>
    <div class="clearfix"></div>
</div>
<div class="x_content">
    <div class="dashboard-widget-content">
        <ul class="quick-list">
            <li>
                <span class="meta-data-title">
                    <i class="fa fa-desktop fa-lg"></i>
                    <i class="fa fa-mobile fa-lg"></i>
                </span>
                <span class="meta-data-data">
                    
                    
                    
                    
                    <i class="fa fa-desktop fa-lg">
                        <span>desktop</span>
                    </i>
                </span>
            </li>
            <li>
                <span class="meta-data-title">Device</span>
                <span class="meta-data-data">Local test machine</span>
            </li>
            <li>
                <span class="meta-data-title">OS</span>
                <span class="meta-data-data">
                    
                    
                    
                    
                    <i class="fa fa-windows fa-lg">
                        <span>windows</span>
                    </i>
                    10
                </span>
            </li>
            
            
            <li>
                <span class="meta-data-title">Browser</span>
                <span class="meta-data-data">
                    
                    
                    
                    
                    
                    <i class="fa fa-chrome fa-lg">
                        <span>chrome</span>
                    </i>
                    114
                    
                </span>
            </li>
            
        </ul>
    </div>
</div>

                </div>
            </div>
            
        </div>
        <div class="dropdown pull-right dropup" >
            <button class="btn btn-info dropdown-toggle btn-sm" type="button" data-bs-toggle="dropdown" style="margin-bottom: 15px;">
                Filter By<span class="caret" style="margin-left: 5px"></span></button>
            <ul class="dropdown-menu">
                <li> <a id="passed" href="javascript:void(0)" onclick="hideResult(this.id)">Passed
                    <i class="fa fa-check-circle passed-color filter-i"></i></a>
                </li>
                <li> <a id="failed" href="javascript:void(0)" onclick="hideResult(this.id)">Failed
                    <i class="fa fa-exclamation-circle failed-color filter-i"></i></a>
                </li>
                <li> <a id="pending" href="javascript:void(0)" onclick="hideResult(this.id)">Pending
                    <i class="fa fa-minus-circle pending-color filter-i"></i></a>
                </li>
                <li> <a id="skipped" href="javascript:void(0)" onclick="hideResult(this.id)">Skipped
                    <i class="fa fa-arrow-circle-right fa skipped-color filter-i"></i></a>
                </li>
                <li> <a id="not-defined" href="javascript:void(0)" onclick="hideResult(this.id)">Not Defined
                    <i class="fa fa-question-circle not-defined-color filter-i"></i></a>
                </li>
                <li> <a id="ambiguous" href="javascript:void(0)" onclick="hideResult(this.id)">Ambiguous
                    <i class="fa fa-flash ambiguous-color filter-i"></i></a>
                </li>
                <li class="divider"></li>
                <li> <a id="clear" href="javascript:void(0)"onclick="showAll()">Clear
                    <i class="fa fa-times-circle"></i></a>
                </li>
            </ul>
        </div>
        <div class="row">
            
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel" style="height: auto;">
            <div class="x_title">

                <div class="tags">
                    
                        
                        
                    
                </div>

                <h2>Scenario: <small>Validate user is able to login Successfully</small></h2>

                <ul class="nav navbar-right panel_toolbox">
                    <li>
                        <a class="collapse-link"><i class="fa fa-chevron-down"></i></a>
                    </li>

                    

                    

                    

                    

                    
                        <li>
                            <span class="step passed-background" data-bs-toggle="tooltip" title="Scenario passed">4</span>
                        </li>
                    

                    

                    
                </ul>

                <div class="clearfix"></div>
            </div>

            <div class="x_content" style="display: none;">
                <div class="scenario-step-container"></div>
                    
                        
                
                        
                            <div class="scenario-step-container">

                                
                                    
                                        <div class="label" title="Success">
                                            <i class="fa fa-check-circle fa-2x passed-color" data-bs-toggle="tooltip" data-placement="top" title="Step passed"></i>
                                        </div>
                                    
                                

                                <div class="text">
                                    <span class="keyword highlight">Given </span>

                                    
                                        User is on the login page
                                        
                                    

                                    

                                </div>

                                
                                    

                                    

                                    

                                    

                                    

                                    

                                    

                                    
                                
                            </div>

                        
                            

                            

                            

                            

                            

                            

                            
                        

                        
                            
                        

                    
                
                        
                            <div class="scenario-step-container">

                                
                                    
                                        <div class="label" title="Success">
                                            <i class="fa fa-check-circle fa-2x passed-color" data-bs-toggle="tooltip" data-placement="top" title="Step passed"></i>
                                        </div>
                                    
                                

                                <div class="text">
                                    <span class="keyword highlight">And </span>

                                    
                                        user enters valid username and password
                                        
                                    

                                    

                                </div>

                                
                                    

                                    

                                    

                                    

                                    

                                    

                                    

                                    
                                
                            </div>

                        
                            

                            

                            

                            

                            

                            

                            
                        

                        
                            
                        

                    
                
                        
                            <div class="scenario-step-container">

                                
                                    
                                        <div class="label" title="Success">
                                            <i class="fa fa-check-circle fa-2x passed-color" data-bs-toggle="tooltip" data-placement="top" title="Step passed"></i>
                                        </div>
                                    
                                

                                <div class="text">
                                    <span class="keyword highlight">When </span>

                                    
                                        user clicks the login button
                                        
                                    

                                    

                                </div>

                                
                                    

                                    

                                    

                                    

                                    

                                    

                                    

                                    
                                
                            </div>

                        
                            

                            

                            

                            

                            

                            

                            
                        

                        
                            
                        

                    
                
                        
                            <div class="scenario-step-container">

                                
                                    
                                        <div class="label" title="Success">
                                            <i class="fa fa-check-circle fa-2x passed-color" data-bs-toggle="tooltip" data-placement="top" title="Step passed"></i>
                                        </div>
                                    
                                

                                <div class="text">
                                    <span class="keyword highlight">Then </span>

                                    
                                        user should be redirected to the dashboard
                                        
                                    

                                    

                                </div>

                                
                                    

                                    

                                    

                                    

                                    

                                    

                                    

                                    
                                
                            </div>

                        
                            

                            

                            

                            

                            

                            

                            
                        

                        
                            
                        

                    
                
                        
                
            </div>
        </div>
    </div>


        </div>
    </div>

    
        <div class="created-by">
            <p>Created by wswebcreation. Find me on:</p>
            <a href="http://www.wswebcreation.nl/" target="_blank"><i class="fa fa-rss-square fa-2x"></i></a>
            <a href="https://github.com/wswebcreation/" target="_blank"><i class="fa fa-github-square fa-2x"></i></a>
            <a href="http://nl.linkedin.com/in/wimselles" target="_blank"><i class="fa fa-linkedin-square fa-2x"></i></a>
            <a href="http://stackoverflow.com/users/5911978/wswebcreation" target="_blank"><i class="fa fa-stack-overflow fa-2x"></i></a>
        </div>
    

    
        <script src="../assets/js/jquery.min.js"></script>
        <script src="../assets/js/bootstrap.min.js"></script>
        <script src="../assets/js/Chart.min.js"></script>

        <script src="../assets/js/datatables.jquery.min.js"></script>
        <script src="../assets/js/datatables.min.js"></script>
        <script src="../assets/js/datatables.bootstrap5.min.js"></script>
        <script src="../assets/js/dataTables.responsive.min.js"></script>
        <script src="../assets/js/responsive.bootstrap5.js"></script>
    

    <!-- Custom -->
    <script>
        var hideResult;
        var showAll;
        $(document).ready(function () {

            var scenarioOptions = {
                legend: false,
                responsive: false
            };

            var getColor = function(selector, defaultColor) {
                if (document.querySelector(selector)) {
                    return getComputedStyle(document.querySelector(selector)).color
                }
                return defaultColor
            }

            new Chart(document.getElementById("scenario-chart"), {
                type: 'doughnut',
                tooltipFillColor: "rgba(51, 51, 51, 0.55)",
                data: {
                    labels: [
                        "Passed",
                        "Failed",
                        "Pending",
                        "Skipped",
                        "Ambiguous",
                        "Not defined"
                    ],
                    datasets: [{
                        data: [
                            1,
                            0,
                            0,
                            0,
                            0,
                            0
                        ],
                        backgroundColor: [
                            getColor(".passed-color", "#26B99A"),
                            getColor(".failed-color", "#E74C3C"),
                            getColor(".pending-color", "#FFD119"),
                            getColor(".skipped-color", "#3498DB"),
                            getColor(".ambiguous-color", "#b73122"),
                            getColor(".not-defined-color", "#F39C12")
                        ]
                    }]
                },
                options: scenarioOptions
            });

            $(".x_title").on("click", function () {
  var $BOX_PANEL = $(this).closest(".x_panel"),
    $ICON = $(this).find(".collapse-link i"),
    $BOX_CONTENT = $BOX_PANEL.find(".x_content");

  // fix for some div with hardcoded fix class
  if ($BOX_PANEL.attr("style")) {
    $BOX_CONTENT.slideToggle(200, function () {
      $BOX_PANEL.removeAttr("style");
    });
  } else {
    $BOX_CONTENT.slideToggle(200);
    $BOX_PANEL.css("height", "auto");
  }

  $ICON.toggleClass("fa-chevron-up fa-chevron-down");
});

$("body").tooltip({
  selector: '[data-bs-toggle="tooltip"]',
});

hideResult = (resultId) => {
  $("span[class*=step]").closest("div.x_panel[style]").hide();
  $("span[class*=" + resultId + "]")
    .closest("div.x_panel[style]")
    .show();
};

showAll = () => {
  $("span[class*=step]").closest("div.x_panel[style]").show();
};

$(document).ready(() => {
  const status = [
    "passed",
    "failed",
    "pending",
    "skipped",
    "ambiguous",
    "not-defined",
  ];
  status.forEach((value) => {
    var menuItem = $("span[class*=" + value + "-background]");
    if (menuItem.length === 0) {
      $("#" + value)
        .parent()
        .addClass("disabled");
    }
  });
});

        });
    </script>
</body>
</html>
